"""
Payment and wallet management models
"""

from django.db import models, transaction
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from accounts.models import CustomUser

User = get_user_model()


class Transaction(models.Model):
    """
    Model to track all financial operations with comprehensive audit trail
    """
    TRANSACTION_TYPE_CHOICES = [
        ('deposit', 'Deposit'),
        ('withdrawal', 'Withdrawal'),
        ('bet_stake', 'Bet Stake'),
        ('bet_winnings', 'Bet Winnings'),
        ('bet_refund', 'Bet Refund'),
        ('bonus', 'Bonus'),
        ('penalty', 'Penalty'),
        ('adjustment', 'Manual Adjustment'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('reversed', 'Reversed'),
    ]
    
    PAYMENT_METHOD_CHOICES = [
        ('mpesa', 'M-Pesa'),
        ('bank_transfer', 'Bank Transfer'),
        ('card', 'Credit/Debit Card'),
        ('paypal', 'PayPal'),
        ('stripe', 'Stripe'),
        ('system', 'System Generated'),
    ]
    
    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='transactions'
    )
    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPE_CHOICES
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending'
    )
    
    # Payment details
    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHOD_CHOICES,
        null=True,
        blank=True
    )
    external_transaction_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Transaction ID from external payment provider"
    )
    
    # Balance tracking
    balance_before = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="User balance before this transaction"
    )
    balance_after = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="User balance after this transaction"
    )
    
    # Metadata and audit trail
    description = models.TextField(
        blank=True,
        help_text="Description of the transaction"
    )
    metadata = models.JSONField(
        default=dict,
        help_text="Additional transaction metadata"
    )
    
    # Related objects
    related_bet_id = models.UUIDField(
        null=True,
        blank=True,
        help_text="Related bet ID if transaction is betting-related"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    processed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the transaction was processed"
    )
    
    # Admin fields
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_transactions',
        help_text="Admin user who processed this transaction"
    )
    admin_notes = models.TextField(
        blank=True,
        help_text="Internal admin notes"
    )
    
    class Meta:
        db_table = 'payments_transaction'
        verbose_name = 'Transaction'
        verbose_name_plural = 'Transactions'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['transaction_type', '-created_at']),
            models.Index(fields=['external_transaction_id']),
        ]
    
    def __str__(self):
        user_identifier = getattr(self.user, 'phone_number', 'Unknown User') if self.user else "Unknown User"
        return f"{user_identifier} - {self.transaction_type} - {self.amount}"
    
    def save(self, *args, **kwargs):
        """Override save to set processed_at when status changes to completed"""
        if self.status == 'completed' and not self.processed_at:
            self.processed_at = timezone.now()
        super().save(*args, **kwargs)
    
    @property
    def is_credit(self):
        """Check if transaction adds money to user balance"""
        return self.transaction_type in ['deposit', 'bet_winnings', 'bet_refund', 'bonus']
    
    @property
    def is_debit(self):
        """Check if transaction removes money from user balance"""
        return self.transaction_type in ['withdrawal', 'bet_stake', 'penalty']


class WalletManager:
    """
    Manager class for wallet operations with atomic transactions
    """
    
    @staticmethod
    @transaction.atomic
    def create_transaction(user, transaction_type, amount, **kwargs):
        """
        Create a new transaction with atomic balance update
        
        Args:
            user: User instance
            transaction_type: Type of transaction
            amount: Transaction amount
            **kwargs: Additional transaction fields
        
        Returns:
            Transaction instance
        """
        # Lock the user row to prevent race conditions
        user = CustomUser.objects.select_for_update().get(pk=user.pk)
        
        # Record balance before transaction
        balance_before = user.balance
        
        # Calculate new balance
        if transaction_type in ['deposit', 'bet_winnings', 'bet_refund', 'bonus']:
            balance_after = balance_before + Decimal(str(amount))
        elif transaction_type in ['withdrawal', 'bet_stake', 'penalty']:
            balance_after = balance_before - Decimal(str(amount))
        else:
            # For adjustments, amount can be positive or negative
            balance_after = balance_before + Decimal(str(amount))
        
        # Validate sufficient balance for debit transactions
        if balance_after < 0 and transaction_type in ['withdrawal', 'bet_stake']:
            raise InsufficientBalanceError(f"Insufficient balance. Current: {balance_before}, Required: {amount}")
        
        # Create transaction record
        transaction_obj = Transaction.objects.create(
            user=user,
            transaction_type=transaction_type,
            amount=abs(Decimal(str(amount))),
            balance_before=balance_before,
            balance_after=balance_after,
            **kwargs
        )
        
        # Update user balance
        user.balance = balance_after
        user.save(update_fields=['balance'])
        
        return transaction_obj
    
    @staticmethod
    @transaction.atomic
    def process_deposit(user, amount, payment_method, external_transaction_id=None, **kwargs):
        """
        Process a deposit transaction
        
        Args:
            user: User instance
            amount: Deposit amount
            payment_method: Payment method used
            external_transaction_id: External payment provider transaction ID
            **kwargs: Additional metadata
        
        Returns:
            Transaction instance
        """
        return WalletManager.create_transaction(
            user=user,
            transaction_type='deposit',
            amount=amount,
            payment_method=payment_method,
            external_transaction_id=external_transaction_id,
            status='completed',
            description=f"Deposit via {payment_method}",
            metadata=kwargs
        )
    
    @staticmethod
    @transaction.atomic
    def process_withdrawal(user, amount, payment_method, **kwargs):
        """
        Process a withdrawal transaction
        
        Args:
            user: User instance
            amount: Withdrawal amount
            payment_method: Payment method for withdrawal
            **kwargs: Additional metadata
        
        Returns:
            Transaction instance
        """
        return WalletManager.create_transaction(
            user=user,
            transaction_type='withdrawal',
            amount=amount,
            payment_method=payment_method,
            status='pending',
            description=f"Withdrawal via {payment_method}",
            metadata=kwargs
        )
    
    @staticmethod
    @transaction.atomic
    def process_bet_stake(user, amount, bet_id, **kwargs):
        """
        Process bet stake deduction
        
        Args:
            user: User instance
            amount: Stake amount
            bet_id: Related bet ID
            **kwargs: Additional metadata
        
        Returns:
            Transaction instance
        """
        return WalletManager.create_transaction(
            user=user,
            transaction_type='bet_stake',
            amount=amount,
            related_bet_id=bet_id,
            status='completed',
            description=f"Bet stake for bet {bet_id}",
            metadata=kwargs
        )
    
    @staticmethod
    @transaction.atomic
    def process_bet_winnings(user, amount, bet_id, **kwargs):
        """
        Process bet winnings credit
        
        Args:
            user: User instance
            amount: Winnings amount
            bet_id: Related bet ID
            **kwargs: Additional metadata
        
        Returns:
            Transaction instance
        """
        return WalletManager.create_transaction(
            user=user,
            transaction_type='bet_winnings',
            amount=amount,
            related_bet_id=bet_id,
            status='completed',
            description=f"Bet winnings for bet {bet_id}",
            metadata=kwargs
        )
    
    @staticmethod
    def get_user_balance(user):
        """Get current user balance"""
        return user.balance
    
    @staticmethod
    def get_transaction_history(user, transaction_type=None, limit=None):
        """
        Get user transaction history
        
        Args:
            user: User instance
            transaction_type: Filter by transaction type
            limit: Limit number of results
        
        Returns:
            QuerySet of transactions
        """
        queryset = Transaction.objects.filter(user=user)
        
        if transaction_type:
            queryset = queryset.filter(transaction_type=transaction_type)
        
        if limit:
            queryset = queryset[:limit]
        
        return queryset


class InsufficientBalanceError(Exception):
    """Exception raised when user has insufficient balance"""
    pass


class PaymentMethod(models.Model):
    """
    Model to store user payment methods
    """
    PAYMENT_TYPE_CHOICES = [
        ('mpesa', 'M-Pesa'),
        ('bank_account', 'Bank Account'),
        ('card', 'Credit/Debit Card'),
    ]
    
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='payment_methods'
    )
    payment_type = models.CharField(
        max_length=20,
        choices=PAYMENT_TYPE_CHOICES
    )
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    
    # M-Pesa specific fields
    mpesa_phone_number = models.CharField(
        max_length=15,
        blank=True,
        help_text="M-Pesa registered phone number"
    )
    
    # Bank account specific fields
    bank_name = models.CharField(max_length=100, blank=True)
    account_number = models.CharField(max_length=50, blank=True)
    account_name = models.CharField(max_length=100, blank=True)
    
    # Card specific fields (stored securely)
    card_last_four = models.CharField(
        max_length=4,
        blank=True,
        help_text="Last four digits of card"
    )
    card_brand = models.CharField(
        max_length=20,
        blank=True,
        help_text="Card brand (Visa, Mastercard, etc.)"
    )
    
    # Metadata
    metadata = models.JSONField(
        default=dict,
        help_text="Additional payment method metadata"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    verified_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'payments_paymentmethod'
        verbose_name = 'Payment Method'
        verbose_name_plural = 'Payment Methods'
        unique_together = [
            ('user', 'payment_type', 'mpesa_phone_number'),
            ('user', 'payment_type', 'account_number'),
        ]
    
    def __str__(self):
        if self.payment_type == 'mpesa':
            return f"{self.user.phone_number} - M-Pesa ({self.mpesa_phone_number})"
        elif self.payment_type == 'bank_account':
            return f"{self.user.phone_number} - {self.bank_name} ({self.account_number})"
        elif self.payment_type == 'card':
            return f"{self.user.phone_number} - {self.card_brand} ****{self.card_last_four}"
        return f"{self.user.phone_number} - {self.payment_type}"
